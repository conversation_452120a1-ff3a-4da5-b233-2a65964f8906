extends Node

signal done()
var all : Array[int]

func _ready():
	var file := FileAccess.open("res://allwikiPrimes.txt", FileAccess.READ)
	var lines : PackedStringArray = file.get_csv_line()
	for i:int in lines.size():
		all.append(lines[i].to_int())


func _compare(primes:Array[int]):
	printerr("~~~COMPARE START~~~")
	for i in min(primes.size(), all.size()):
		if primes[i] != all[i]:
			printerr(primes[i]," != ",all[i])
	printerr("~~~COMPARE COMPLETE~~~")
	print("Final 5 Primes", primes.slice(-5))
	print(Time.get_ticks_msec() * 0.001, " Seconds")
	await get_tree().create_timer(1.0).timeout
	done.emit()
