extends Node

signal done()
var preloaded : Array[int]
var all : Array[int]

func _ready():
	preloaded = _load_csv("res://allwikiPrimes_small.txt")
	all = _load_csv("res://allwikiPrimes.txt")
func _load_csv(filepath:String)->Array[int]:
	var file := FileAccess.open(filepath, FileAccess.READ)
	var lines : PackedStringArray = file.get_csv_line()
	var ret : Array[int]
	for i:int in lines.size():
		ret.append(lines[i].to_int())
	return ret


func _compare(primes:Array[int]):
	printerr("~~~COMPARE START~~~")
	for i in min(primes.size(), all.size()):
		if primes[i] != all[i]:
			printerr(primes[i]," != ",all[i])
	printerr("~~~COMPARE COMPLETE~~~")
	print("Final 5 Primes", primes.slice(-5))
	print(Time.get_ticks_msec() * 0.001, " Seconds")
	await get_tree().create_timer(1.0).timeout
	done.emit()
