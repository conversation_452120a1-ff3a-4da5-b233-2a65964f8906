extends Control

@export_range(1,100,1) var num_per_frame : int = 1

var primes : Array[int] = [2,3]
var are_primes : Dictionary[int,bool] = {2:true,3:true}

func check_old(i:int)->bool:
	for key in primes:
		if are_primes.has(i - key):
			return true
	return false
func new_sign(i:int):
	var j : int = primes[-1]
	while j <= i:
		if i - j in primes:
			if is_prime(j):
				return j
		j += 2
	if !is_prime(i):
		return -i
	return i
func is_prime(p:int)->bool:
	if p in are_primes:
		return are_primes[p]
	var is_p : bool = true
	# Check for divisibility by odd numbers from 3 up to the square root of 'p'
	for i in range(3, ceil(sqrt(p)) + 1, 2):
		if p % i == 0:
			is_p = false
			break
	are_primes[p] = is_p
	return is_p
var itt : int = primes[-1]+1
func _process(_delta:float):
	for i in num_per_frame:
		_main()
func _main():
	if itt > 20000:
		_on_pressed()
	if itt < 1:
		return
	print("Step : ",itt)
	if !check_old(itt):
		if itt % 2 == 0:
			_on_pressed()
		var new : int = new_sign(itt)
		if new > 1:
			primes.append( new )
			print("New Prime: ", new)
	itt += 1



func _on_pressed():
	print("Primes : ",primes)
	print("exit at:",itt)
	itt = -1
	$known._compare(primes)

func _ready():
	primes = $known.preloaded
	itt = primes[-1] +1
	get_tree().set_auto_accept_quit(false)
	print("New Prime: 2")
	print("New Prime: 3")
func _notification(what):
	if what == NOTIFICATION_WM_CLOSE_REQUEST:
		_on_pressed()
func _on_known_done():
	get_tree().quit()
