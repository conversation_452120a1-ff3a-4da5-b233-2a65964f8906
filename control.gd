extends Control

@export_range(1,100,1) var num_per_frame : int = 1

var primes : Array[int] = [2,3]
func check_old(i:int)->bool:
	for key in primes:
		if i - key in primes:
			return true
	return false
func new_sign(i:int):
	var j : int = 3
	while j <= i:
		while j in primes:
			j += 2
		for key in primes:
			if j + key == i:
				if _check_is_prime(j):
					return j
		j += 2
	if !_check_is_prime(i):
		return -i
	return i
func _check_is_prime(p):
	for i in p-2:
		if i * i > p:
			return true
		if p % (i+2) == 0:
			return false
	return true
var itt : int = 4
func _process(delta):
	for i in num_per_frame:
		_main()
func _main():
	#if itt > 8000:
		#_on_pressed()
	if itt < 1:
		return
	print("Step : ",itt)
	if !check_old(itt):
		if itt % 2 == 0:
			_on_pressed()
		var new : int = new_sign(itt)
		if new > 1:
			primes.append( new )
			print("New Prime: ", new)
	itt += 1



func _on_pressed():
	print("Primes : ",primes)
	print("exit at:",itt)
	itt = -1
	$known._compare(primes)

func _ready():
	get_tree().set_auto_accept_quit(false)
	print("New Prime: 2")
	print("New Prime: 3")
func _notification(what):
	if what == NOTIFICATION_WM_CLOSE_REQUEST:
		_on_pressed()
func _on_known_done():
	get_tree().quit()
